{"cells": [{"cell_type": "code", "execution_count": null, "id": "b7824f2e", "metadata": {"vscode": {"languageId": "markdown"}}, "outputs": [], "source": ["# <PERSON>CKEND ENGINEER HOME TEST"]}, {"cell_type": "code", "execution_count": null, "id": "3f2b2cab", "metadata": {"vscode": {"languageId": "markdown"}}, "outputs": [], "source": ["## Question 1\n", "\n", "**Using Java or Python, implement an idempotency key mechanism for a RESTful API that processes payment transactions.**\n", "\n", "Your implementation should:\n", "\n", "1. Accept an idempotency key in API requests  \n", "2. Store the request and response associated with each idempotency key  \n", "3. Return the payment transaction response when the same idempotency key is reused  \n", "4. <PERSON>le concurrent requests with the same idempotency key correctly  \n", "5. Set appropriate expiration for stored idempotency keys  \n", "\n", "---\n", "\n", "**Solution:**  \n", "*(Please see the code implementation below)*"]}, {"cell_type": "markdown", "id": "44bfecc9", "metadata": {}, "source": ["The solution implements a distributed idempotency mechanism using Redis for storage and locking with banking-grade validation:\n", "\n", "```mermaid\n", "sequenceDiagram\n", "    participant Client\n", "    participant API\n", "    participant <PERSON><PERSON>\n", "    participant PaymentGateway\n", "\n", "    Client->>API: POST /payments (Idempotency-Key + Body)\n", "\n", "    Note over API: IdempotencyService.validate_idempotency_key()\n", "    API->>API: Validate Key Format & Length\n", "    alt Invalid Key Format\n", "        API-->>Client: 400 Bad Request\n", "    else Valid Key\n", "        Note over API: Hash request body for integrity\n", "        API->>API: Generate SHA-256 Hash\n", "\n", "        API->>Redis: GET cached response\n", "        alt Cache Hit\n", "            Redis-->>API: Cached Response + Body Hash\n", "            API->>API: Validate Body Hash Match\n", "            alt Body Hash Mismatch\n", "                API-->>Client: 422 Unprocessable Entity\n", "            else Body Hash Match\n", "                API-->>Client: Return Cached Response (201)\n", "            end\n", "        else <PERSON><PERSON>\n", "            API->>Redis: SETNX acquire lock\n", "\n", "            alt Lock Acquired\n", "                Redis-->>API: Lock Granted\n", "                API->>PaymentGateway: Process Payment\n", "                PaymentGateway-->>API: Payment Result\n", "                API->>Redis: SET cache response + TTL\n", "                API->>Redis: DEL release lock\n", "                API-->>Client: Return Payment Response (201)\n", "            else Lock Failed (Concurrent Request)\n", "                Redis-->>API: Lock Denied\n", "                loop Poll for Result (max 5s)\n", "                    API->>Redis: GET check for cached result\n", "                    alt Result Available\n", "                        Redis-->>API: Response Found\n", "                        API-->>Client: Return Response (201)\n", "                    else Still Processing\n", "                        API->>API: Wait 50ms\n", "                    end\n", "                end\n", "                alt Timeout Reached\n", "                    API-->>Client: 409 Conflict\n", "                end\n", "            end\n", "        end\n", "    end\n", "```"]}, {"cell_type": "code", "execution_count": null, "id": "1003d56d", "metadata": {"vscode": {"languageId": "markdown"}}, "outputs": [], "source": ["\"\"\"\n", "Idempotency Key Implementation for Payment API\n", "\n", "This module provides a idempotency mechanism with:\n", "- Distributed locking using Redis\n", "- Request body validation and hashing\n", "- Error handling and logging\n", "- Configurable timeouts and TTL\n", "- Health monitoring and metrics\n", "\"\"\"\n", "\n", "from fastapi import FastAPI, Header, HTTPException, Response, status, Depends\n", "from pydantic import BaseModel, Field, field_validator\n", "from redis import Redis, ConnectionError as RedisConnectionError\n", "import json\n", "import hashlib\n", "import time\n", "import logging\n", "import os\n", "import re\n", "from typing import Optional, Dict, Any\n", "\n", "from datetime import datetime, timezone\n", "import uuid\n", "\n", "# Configure logging\n", "logging.basicConfig(\n", "    level=logging.INFO,\n", "    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'\n", ")\n", "logger = logging.getLogger(__name__)\n", "\n", "# Configuration from environment variables\n", "class Config:\n", "    REDIS_URL = os.getenv(\"REDIS_URL\", \"redis://localhost:6379/0\")\n", "    IDEMPOTENCY_KEY_HEADER = os.getenv(\"IDEMPOTENCY_KEY_HEADER\", \"Idempotency-Key\")\n", "    IDEMPOTENCY_TTL_SECONDS = int(os.getenv(\"IDEMPOTENCY_TTL_SECONDS\", \"86400\"))  # 24h\n", "    IDEMPOTENCY_LOCK_TTL = int(os.getenv(\"IDEMPOTENCY_LOCK_TTL\", \"30\"))  # 30s\n", "    REQUEST_WAIT_TIMEOUT = int(os.getenv(\"REQUEST_WAIT_TIMEOUT\", \"5\"))  # 5s\n", "    MAX_RETRIES = int(os.getenv(\"MAX_RETRIES\", \"3\"))\n", "\n", "config = Config()\n", "\n", "# Redis connection with retry logic\n", "def create_redis_connection() -> Redis:\n", "    \"\"\"Create Redis connection with error handling.\"\"\"\n", "    try:\n", "        redis_client = Redis.from_url(\n", "            config.REDIS_URL,\n", "            decode_responses=True,\n", "            socket_connect_timeout=5,\n", "            socket_timeout=5,\n", "            retry_on_timeout=True\n", "        )\n", "        # Test connection\n", "        redis_client.ping()\n", "        logger.info(\"Redis connection established successfully\")\n", "        return redis_client\n", "    except Exception as e:\n", "        logger.error(f\"Failed to connect to Redis: {e}\")\n", "        raise\n", "\n", "redis = create_redis_connection()\n", "\n", "# Pydantic models with validation\n", "class PaymentRequest(BaseModel):\n", "    amount: float = Field(..., gt=0, description=\"Payment amount (must be positive)\")\n", "    currency: str = Field(..., min_length=3, max_length=3, description=\"ISO currency code\")\n", "    card_token: str = Field(..., min_length=1, description=\"Tokenized card information\")\n", "    \n", "    @field_validator('currency')\n", "    @classmethod\n", "    def validate_currency(cls, v):\n", "        return v.upper()\n", "\n", "    @field_validator('amount')\n", "    @classmethod\n", "    def validate_amount(cls, v):\n", "        # Round to 2 decimal places for currency\n", "        return round(v, 2)\n", "\n", "class PaymentResponse(BaseModel):\n", "    transaction_id: str\n", "    status: str\n", "    amount: float\n", "    currency: str\n", "    created_at: str\n", "    \n", "class IdempotencyService:\n", "    \"\"\"Service class for handling idempotency logic.\"\"\"\n", "\n", "    def __init__(self, redis_client: Redis):\n", "        self.redis = redis_client\n", "\n", "    def validate_idempotency_key(self, key: str) -> str:\n", "        \"\"\"\n", "        Validate idempotency key according to banking industry best practices.\n", "\n", "        Args:\n", "            key: The idempotency key to validate\n", "\n", "        Returns:\n", "            str: The validated and normalized key\n", "\n", "        Raises:\n", "            HTTPException: If key is invalid\n", "        \"\"\"\n", "        # Check for empty or None key\n", "        if not key or len(key.strip()) == 0:\n", "            raise HTTPException(\n", "                status_code=status.HTTP_400_BAD_REQUEST,\n", "                detail=\"Idempotency key cannot be empty\"\n", "            )\n", "\n", "        # Normalize key (strip whitespace)\n", "        normalized_key = key.strip()\n", "\n", "        # Banking industry validation: Length checks\n", "        if len(normalized_key) > 255:\n", "            raise HTTPException(\n", "                status_code=status.HTTP_400_BAD_REQUEST,\n", "                detail=\"Idempotency key must be 255 characters or less\"\n", "            )\n", "\n", "        if len(normalized_key) < 1:\n", "            raise HTTPException(\n", "                status_code=status.HTTP_400_BAD_REQUEST,\n", "                detail=\"Idempotency key must be at least 1 character\"\n", "            )\n", "\n", "        # Validate character set (alphanumeric, hyphens, underscores - safe for headers)\n", "        import re\n", "        if not re.match(r'^[a-zA-Z0-9\\-_]+$', normalized_key):\n", "            raise HTTPException(\n", "                status_code=status.HTTP_400_BAD_REQUEST,\n", "                detail=\"Idempotency key contains invalid characters. Use only alphanumeric, hyphens, and underscores\"\n", "            )\n", "\n", "        return normalized_key\n", "        \n", "    def _generate_cache_key(self, idempotency_key: str) -> str:\n", "        \"\"\"Generate Redis cache key for idempotency.\"\"\"\n", "        return f\"idem:{idempotency_key}\"\n", "    \n", "    def _generate_lock_key(self, idempotency_key: str) -> str:\n", "        \"\"\"Generate Redis lock key for idempotency.\"\"\"\n", "        return f\"idem:{idempotency_key}:lock\"\n", "    \n", "    def _hash_request(self, body: Dict[str, Any]) -> str:\n", "        \"\"\"\n", "        Generate SHA-256 hash of request body for integrity validation.\n", "        \n", "        Args:\n", "            body: Request body dictionary\n", "            \n", "        Returns:\n", "            str: SHA-256 hash of the sorted JSON representation\n", "            \n", "        Raises:\n", "            HTTPException: If body cannot be serialized\n", "        \"\"\"\n", "        try:\n", "            # Ensure consistent serialization\n", "            normalized_json = json.dumps(body, sort_keys=True, separators=(',', ':'))\n", "            return hashlib.sha256(normalized_json.encode('utf-8')).hexdigest()\n", "        except (TypeError, ValueError) as e:\n", "            logger.error(f\"Failed to hash request body: {e}\")\n", "            raise HTTPException(\n", "                status_code=status.HTTP_400_BAD_REQUEST,\n", "                detail=\"Invalid request body format\"\n", "            )\n", "    \n", "    def get_cached_response(self, idempotency_key: str, body_hash: str) -> Optional[Dict[str, Any]]:\n", "        \"\"\"\n", "        Retrieve cached response for idempotency key.\n", "        \n", "        Args:\n", "            idempotency_key: The idempotency key\n", "            body_hash: Hash of the request body\n", "            \n", "        Returns:\n", "            Cached response data or None if not found\n", "            \n", "        Raises:\n", "            HTTPException: If key exists but body hash doesn't match\n", "        \"\"\"\n", "        cache_key = self._generate_cache_key(idempotency_key)\n", "        \n", "        try:\n", "            cached = self.redis.get(cache_key)\n", "            if not cached:\n", "                return None\n", "                \n", "            cached_obj = json.loads(cached)\n", "            \n", "            # Validate request body hasn't changed\n", "            if cached_obj.get(\"body_hash\") != body_hash:\n", "                logger.warning(f\"Idempotency key {idempotency_key} reused with different body\")\n", "                raise HTTPException(\n", "                    status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,\n", "                    detail=\"This idempotency key was used with a different request body\",\n", "                )\n", "            \n", "            logger.info(f\"<PERSON><PERSON> hit for idempotency key: {idempotency_key}\")\n", "            return cached_obj\n", "            \n", "        except json.JSONDecodeError as e:\n", "            logger.error(f\"Failed to decode cached response for key {idempotency_key}: {e}\")\n", "            # Clear corrupted cache entry\n", "            self.redis.delete(cache_key)\n", "            return None\n", "        except RedisConnectionError as e:\n", "            logger.error(f\"Redis connection error: {e}\")\n", "            raise HTTPException(\n", "                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,\n", "                detail=\"Cache service temporarily unavailable\"\n", "            )\n", "    \n", "    def acquire_lock(self, idempotency_key: str) -> bool:\n", "        \"\"\"\n", "        Acquire distributed lock for idempotency key.\n", "        \n", "        Args:\n", "            idempotency_key: The idempotency key\n", "            \n", "        Returns:\n", "            bool: True if lock acquired, False otherwise\n", "        \"\"\"\n", "        lock_key = self._generate_lock_key(idempotency_key)\n", "        \n", "        try:\n", "            # Use SETNX with TTL for atomic lock acquisition\n", "            return self.redis.set(\n", "                lock_key, \n", "                f\"locked:{datetime.now(timezone.utc).isoformat()}\", \n", "                nx=True, \n", "                ex=config.IDEMPOTENCY_LOCK_TTL\n", "            )\n", "        except RedisConnectionError as e:\n", "            logger.error(f\"Failed to acquire lock: {e}\")\n", "            return False\n", "    \n", "    def release_lock(self, idempotency_key: str) -> None:\n", "        \"\"\"Release distributed lock for idempotency key.\"\"\"\n", "        lock_key = self._generate_lock_key(idempotency_key)\n", "        \n", "        try:\n", "            self.redis.delete(lock_key)\n", "        except RedisConnectionError as e:\n", "            logger.error(f\"Failed to release lock: {e}\")\n", "    \n", "    def cache_response(self, idempotency_key: str, body_hash: str, \n", "                      response_data: str, http_status: int) -> None:\n", "        \"\"\"\n", "        Cache response for idempotency key.\n", "        \n", "        Args:\n", "            idempotency_key: The idempotency key\n", "            body_hash: Hash of the request body\n", "            response_data: Serialized response data\n", "            http_status: HTTP status code\n", "        \"\"\"\n", "        cache_key = self._generate_cache_key(idempotency_key)\n", "        \n", "        payload = {\n", "            \"body_hash\": body_hash,\n", "            \"response_json\": response_data,\n", "            \"http_status\": http_status,\n", "            \"cached_at\": datetime.now(timezone.utc).isoformat()\n", "        }\n", "        \n", "        try:\n", "            self.redis.set(\n", "                cache_key, \n", "                json.dumps(payload), \n", "                ex=config.IDEMPOTENCY_TTL_SECONDS\n", "            )\n", "            logger.info(f\"Cached response for idempotency key: {idempotency_key}\")\n", "        except RedisConnectionError as e:\n", "            logger.error(f\"Failed to cache response: {e}\")\n", "            # Don't raise exception - response can still be returned\n", "\n", "# Dependency injection\n", "def get_idempotency_service() -> IdempotencyService:\n", "    \"\"\"Dependency injection for IdempotencyService.\"\"\"\n", "    return IdempotencyService(redis)\n", "\n", "# FastAPI application\n", "app = FastAPI(\n", "    title=\"Payment API with Idempotency\",\n", "    description=\"Production-ready payment API with idempotency key support\",\n", "    version=\"1.0.0\"\n", ")\n", "\n", "@app.post(\"/payments\", response_model=PaymentResponse, status_code=201)\n", "async def create_payment(\n", "    payment_request: PaymentRequest,\n", "    idempotency_service: IdempotencyService = Depends(get_idempotency_service),\n", "    idem_key: str = Header(..., alias=config.IDEMPOTENCY_KEY_HEADER),\n", "):\n", "    \"\"\"\n", "    Create a new payment transaction with idempotency support.\n", "    \n", "    The idempotency key ensures that duplicate requests return the same response\n", "    without processing the payment multiple times.\n", "    \"\"\"\n", "    # Validate idempotency key using service method\n", "    idem_key = idempotency_service.validate_idempotency_key(idem_key)\n", "    \n", "    # Convert request to dict for hashing\n", "    body = payment_request.model_dump()\n", "    body_hash = idempotency_service._hash_request(body)\n", "    \n", "    # Check for cached response\n", "    cached_response = idempotency_service.get_cached_response(idem_key, body_hash)\n", "    if cached_response:\n", "        return Response(\n", "            content=cached_response[\"response_json\"],\n", "            status_code=cached_response[\"http_status\"],\n", "            media_type=\"application/json\",\n", "        )\n", "    \n", "    # Try to acquire lock\n", "    if not idempotency_service.acquire_lock(idem_key):\n", "        # Another request is processing - wait for result\n", "        start_time = time.time()\n", "        while time.time() - start_time < config.REQUEST_WAIT_TIMEOUT:\n", "            time.sleep(0.05)  # 50ms polling interval\n", "            \n", "            cached_response = idempotency_service.get_cached_response(idem_key, body_hash)\n", "            if cached_response:\n", "                return Response(\n", "                    content=cached_response[\"response_json\"],\n", "                    status_code=cached_response[\"http_status\"],\n", "                    media_type=\"application/json\",\n", "                )\n", "        \n", "        # Timeout - return conflict\n", "        raise HTTPException(\n", "            status_code=status.HTTP_409_CONFLICT,\n", "            detail=\"Request with this idempotency key is already being processed\",\n", "        )\n", "    \n", "    # Process payment (we own the lock)\n", "    try:\n", "        # Simulate payment processing\n", "        # In production, this would call external payment gateway\n", "        transaction_id = f\"txn_{uuid.uuid4().hex[:12]}\"\n", "        \n", "        response_obj = PaymentResponse(\n", "            transaction_id=transaction_id,\n", "            status=\"approved\",\n", "            amount=payment_request.amount,\n", "            currency=payment_request.currency,\n", "            created_at=datetime.now(timezone.utc).isoformat()\n", "        )\n", "        \n", "        response_json = response_obj.model_dump_json()\n", "        http_status = status.HTTP_201_CREATED\n", "        \n", "        # Cache the response\n", "        idempotency_service.cache_response(idem_key, body_hash, response_json, http_status)\n", "        \n", "        logger.info(f\"Payment processed successfully: {transaction_id}\")\n", "        \n", "        return Response(\n", "            content=response_json,\n", "            status_code=http_status,\n", "            media_type=\"application/json\",\n", "        )\n", "        \n", "    except Exception as e:\n", "        logger.error(f\"Payment processing failed: {e}\")\n", "        raise HTTPException(\n", "            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,\n", "            detail=\"Payment processing failed\"\n", "        )\n", "    finally:\n", "        # Always release the lock\n", "        idempotency_service.release_lock(idem_key)\n", "\n", "@app.get(\"/health\")\n", "def health_check():\n", "    \"\"\"Health check endpoint for monitoring.\"\"\"\n", "    try:\n", "        redis_ping = redis.ping()\n", "        redis_info = redis.info()\n", "        \n", "        return {\n", "            \"status\": \"healthy\",\n", "            \"timestamp\": datetime.now(timezone.utc).isoformat(),\n", "            \"redis\": {\n", "                \"connected\": redis_ping,\n", "                \"memory_usage\": redis_info.get(\"used_memory_human\"),\n", "                \"connected_clients\": redis_info.get(\"connected_clients\")\n", "            }\n", "        }\n", "    except Exception as e:\n", "        logger.error(f\"Health check failed: {e}\")\n", "        raise HTTPException(\n", "            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,\n", "            detail=\"Service unhealthy\"\n", "        )\n", "\n", "if __name__ == \"__main__\":\n", "    import uvicorn\n", "    uvicorn.run(app, host=\"0.0.0.0\", port=8000)\n"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"name": "python", "version": "3.13.3"}}, "nbformat": 4, "nbformat_minor": 5}