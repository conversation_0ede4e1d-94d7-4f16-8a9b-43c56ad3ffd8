#!/usr/bin/env python3
"""
Money transfer service implementation for Question 5.

This module demonstrates a clean solution addressing the critical issues
identified in the original code while maintaining appropriate scope for a home test.
"""

import logging
from datetime import datetime, timezone
from decimal import Decimal, ROUND_HALF_UP
from uuid import uuid4
from typing import Optional
from dataclasses import dataclass

# Monetary precision constant
CENT = Decimal("0.01")

# Domain-specific exceptions
class TransferError(Exception):
    """Base exception for transfer operations."""
    pass

class AccountNotFound(TransferError):
    """Raised when an account cannot be found."""
    pass

class InsufficientFunds(TransferError):
    """Raised when source account has insufficient balance."""
    pass

class DuplicateTransfer(TransferError):
    """Raised when attempting to process a duplicate transfer."""
    pass

@dataclass
class Account:
    """Account entity with balance and metadata."""
    id: str
    balance: Decimal
    last_updated: datetime

class TransferService:
    """
    Money transfer service with proper error handling and transaction safety.

    Fixes the critical issues in the original implementation:
    - Uses database transactions for atomicity
    - Prevents duplicate transfers with transfer IDs
    - Proper decimal precision handling
    - UTC timestamps for consistency
    - Specific exception types for clear error handling
    """

    def __init__(self, account_repository):
        self.account_repository = account_repository
        self.logger = logging.getLogger(__name__)

    def transfer_money(
        self,
        from_account_id: str,
        to_account_id: str,
        raw_amount: Decimal,
        transfer_id: Optional[str] = None
    ) -> bool:
        """
        Transfer money between accounts with proper error handling and atomicity.

        Args:
            from_account_id: Source account ID
            to_account_id: Destination account ID
            raw_amount: Amount to transfer (will be quantized to cents)
            transfer_id: Optional idempotency key to prevent duplicates

        Returns:
            bool: True if transfer successful

        Raises:
            AccountNotFound: When source or destination account doesn't exist
            InsufficientFunds: When source account has insufficient balance
            DuplicateTransfer: When transfer_id already processed
        """
        # Generate transfer ID if not provided for idempotency
        if not transfer_id:
            transfer_id = str(uuid4())

        # Quantize amount to prevent precision issues
        amount = raw_amount.quantize(CENT, ROUND_HALF_UP)

        try:
            # Start database transaction for atomicity
            with self.account_repository.transaction():

                # Check for duplicate transfer (idempotency)
                if hasattr(self.account_repository, 'transfer_exists') and \
                   self.account_repository.transfer_exists(transfer_id):
                    raise DuplicateTransfer(f"Transfer {transfer_id} already processed")

                # Find and validate accounts exist
                source_account = self.account_repository.find_by_id(from_account_id)
                if not source_account:
                    raise AccountNotFound(f"Source account not found: {from_account_id}")

                destination_account = self.account_repository.find_by_id(to_account_id)
                if not destination_account:
                    raise AccountNotFound(f"Destination account not found: {to_account_id}")

                # Validate sufficient funds
                if source_account.balance < amount:
                    raise InsufficientFunds(
                        f"Insufficient funds in account {from_account_id}"
                    )

                # Execute transfer with UTC timestamp (fix timezone issue)
                timestamp = datetime.now(tz=timezone.utc)

                # Update balances atomically
                source_account.balance = source_account.balance - amount
                source_account.last_updated = timestamp
                destination_account.balance = destination_account.balance + amount
                destination_account.last_updated = timestamp

                # Save changes within transaction
                self.account_repository.save(source_account)
                self.account_repository.save(destination_account)

                # Record transfer for audit trail and idempotency (if supported)
                if hasattr(self.account_repository, 'record_transfer'):
                    self.account_repository.record_transfer(
                        transfer_id, from_account_id, to_account_id, amount, timestamp
                    )

            # Log successful transfer
            self.logger.info(
                f"Transfer of {amount} from account {from_account_id} to {to_account_id} completed successfully"
            )
            return True

        except TransferError as e:
            # Log business errors and re-raise
            self.logger.error(f"Transfer failed: {str(e)}")
            raise
        except Exception as e:
            # Log system errors and wrap in domain exception
            self.logger.error(f"Error during transfer: {str(e)}", exc_info=True)
            raise TransferError("Transfer failed due to system error") from e


class UserAccountRepository:
    """Simple repository implementation for demonstration."""

    def __init__(self):
        # In real implementation: initialize database connection
        self.accounts = {}
        self.transfers = set()

    def transaction(self):
        """Return transaction context manager."""
        # In real implementation: return database transaction
        return UserTransaction()

    def find_by_id(self, account_id: str) -> Optional[Account]:
        """Find account by ID."""
        # In real implementation: SELECT * FROM accounts WHERE id = ?
        return self.accounts.get(account_id)

    def save(self, account: Account):
        """Save account changes."""
        # In real implementation: UPDATE accounts SET balance = ?, last_updated = ? WHERE id = ?
        self.accounts[account.id] = account

    def transfer_exists(self, transfer_id: str) -> bool:
        """Check if transfer already processed."""
        # In real implementation: SELECT COUNT(*) FROM transfers WHERE transfer_id = ?
        return transfer_id in self.transfers

    def record_transfer(self, transfer_id: str, from_id: str, to_id: str,
                       amount: Decimal, timestamp: datetime):
        """Record transfer for audit trail."""
        # In real implementation: INSERT INTO transfers (transfer_id, from_account_id, to_account_id, amount, timestamp) VALUES (?, ?, ?, ?, ?)
        self.transfers.add(transfer_id)


class UserTransaction:
    """Simple transaction context manager."""

    def __enter__(self):
        # In real implementation: BEGIN TRANSACTION
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        # In real implementation: COMMIT if no exception, ROLLBACK if exception
        if exc_type is None:
            pass  # COMMIT
        else:
            pass  # ROLLBACK


def main():
    """Demonstration of the fixed TransferService."""
    import logging

    # Setup logging
    logging.basicConfig(level=logging.INFO)

    # Initialize repository and service
    repo = UserAccountRepository()
    service = TransferService(repo)

    # Create demo accounts
    alice = Account("ALICE", Decimal("1000.00"), datetime.now(timezone.utc))
    bob = Account("BOB", Decimal("500.00"), datetime.now(timezone.utc))

    repo.save(alice)
    repo.save(bob)

    print("=== TransferService Demo ===")
    print(f"Initial - Alice: ${alice.balance}, Bob: ${bob.balance}")

    # Demonstrate successful transfer
    try:
        result = service.transfer_money("ALICE", "BOB", Decimal("150.00"))
        print(f"Transfer result: {result}")

        # Show updated balances
        alice_updated = repo.find_by_id("ALICE")
        bob_updated = repo.find_by_id("BOB")
        print(f"Final - Alice: ${alice_updated.balance}, Bob: ${bob_updated.balance}")

    except TransferError as e:
        print(f"Transfer failed: {e}")

    # Demonstrate precision handling
    try:
        print("\n=== Testing Precision ===")
        result = service.transfer_money("BOB", "ALICE", Decimal("25.999"))  # Should round to 26.00
        print(f"Precision test result: {result}")

    except TransferError as e:
        print(f"Precision test failed: {e}")

if __name__ == "__main__":
    main()
