#!/usr/bin/env python3
"""
Tests for the simplified TransferService implementation.

These tests demonstrate that the critical issues from the original code
have been addressed with a clean, focused solution.
"""

import unittest
import logging
from decimal import Decimal
from uuid import uuid4
from datetime import datetime, timezone

from transfer_service import (
    TransferService, UserAccountRepository, Account,
    TransferError, AccountNotFound, InsufficientFunds, DuplicateTransfer,
    CENT
)


class TestTransferService(unittest.TestCase):
    """Test suite for TransferService covering all critical scenarios."""

    def setUp(self):
        """Set up test environment with mock repository."""
        self.repo = UserAccountRepository()

        # Set up logging
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)

        self.service = TransferService(self.repo)

        # Create test accounts
        self._create_test_accounts()

    def _create_test_accounts(self):
        """Create test accounts with initial balances."""
        accounts = [
            Account("ACC001", Decimal("1000.00"), datetime.now(timezone.utc)),
            Account("ACC002", Decimal("500.00"), datetime.now(timezone.utc)),
            Account("ACC003", Decimal("0.00"), datetime.now(timezone.utc)),
        ]

        for account in accounts:
            self.repo.save(account)
    
    def test_successful_transfer(self):
        """Test basic successful transfer between accounts."""
        result = self.service.transfer_money("ACC001", "ACC002", Decimal("100.00"))

        # Verify transfer succeeded
        self.assertTrue(result)

        # Verify balances updated correctly
        source = self.repo.find_by_id("ACC001")
        dest = self.repo.find_by_id("ACC002")

        self.assertEqual(source.balance, Decimal("900.00"))
        self.assertEqual(dest.balance, Decimal("600.00"))

    def test_insufficient_funds(self):
        """Test transfer fails when source account has insufficient funds."""
        with self.assertRaises(InsufficientFunds) as context:
            self.service.transfer_money("ACC002", "ACC001", Decimal("600.00"))

        self.assertIn("Insufficient funds", str(context.exception))

        # Verify balances unchanged
        source = self.repo.find_by_id("ACC002")
        dest = self.repo.find_by_id("ACC001")

        self.assertEqual(source.balance, Decimal("500.00"))
        self.assertEqual(dest.balance, Decimal("1000.00"))

    def test_account_not_found(self):
        """Test transfer fails when account doesn't exist."""
        with self.assertRaises(AccountNotFound):
            self.service.transfer_money("INVALID", "ACC001", Decimal("100.00"))

        with self.assertRaises(AccountNotFound):
            self.service.transfer_money("ACC001", "INVALID", Decimal("100.00"))
    
    def test_decimal_precision_quantization(self):
        """Test that amounts are properly quantized to cents."""
        # Transfer amount with more than 2 decimal places
        result = self.service.transfer_money("ACC001", "ACC002", Decimal("100.999"))

        # Should be rounded to 101.00
        self.assertTrue(result)
        source = self.repo.find_by_id("ACC001")
        dest = self.repo.find_by_id("ACC002")

        self.assertEqual(source.balance, Decimal("899.00"))  # 1000 - 101
        self.assertEqual(dest.balance, Decimal("601.00"))    # 500 + 101

    def test_idempotency_duplicate_prevention(self):
        """Test that duplicate transfers with same ID are prevented."""
        transfer_id = str(uuid4())

        # First transfer should succeed
        result = self.service.transfer_money("ACC001", "ACC002", Decimal("100.00"), transfer_id)
        self.assertTrue(result)

        # Second transfer with same ID should fail
        with self.assertRaises(DuplicateTransfer):
            self.service.transfer_money("ACC001", "ACC002", Decimal("100.00"), transfer_id)

        # Verify only one transfer occurred
        source = self.repo.find_by_id("ACC001")
        dest = self.repo.find_by_id("ACC002")

        self.assertEqual(source.balance, Decimal("900.00"))
        self.assertEqual(dest.balance, Decimal("600.00"))
    
    def test_atomic_transaction_rollback(self):
        """Test that failed transfers don't leave partial state."""
        # Simulate a failure by using invalid account in destination
        try:
            self.service.transfer_money("ACC001", "INVALID", Decimal("100.00"))
        except AccountNotFound:
            pass

        # Verify source account balance unchanged
        source = self.repo.find_by_id("ACC001")
        self.assertEqual(source.balance, Decimal("1000.00"))

    def test_sequential_transfers(self):
        """Test that sequential transfers work correctly."""
        # Test sequential transfers to verify logic works
        successful_transfers = 0
        for _ in range(10):
            try:
                result = self.service.transfer_money("ACC001", "ACC002", Decimal("10.00"))
                if result:
                    successful_transfers += 1
            except InsufficientFunds:
                break  # No more funds available

        # Verify final balances are consistent
        source = self.repo.find_by_id("ACC001")
        dest = self.repo.find_by_id("ACC002")

        expected_transferred = Decimal(str(successful_transfers * 10))
        self.assertEqual(source.balance, Decimal("1000.00") - expected_transferred)
        self.assertEqual(dest.balance, Decimal("500.00") + expected_transferred)

        # Should have transferred at most $1000 (all available funds)
        self.assertLessEqual(expected_transferred, Decimal("1000.00"))
    
    def test_bidirectional_transfers(self):
        """Test transfers in both directions work correctly."""
        # Transfer from ACC001 to ACC002
        result1 = self.service.transfer_money("ACC001", "ACC002", Decimal("50.00"))
        self.assertTrue(result1)

        # Transfer from ACC002 to ACC001 (reverse direction)
        result2 = self.service.transfer_money("ACC002", "ACC001", Decimal("25.00"))
        self.assertTrue(result2)

        # Verify both transfers completed successfully
        source = self.repo.find_by_id("ACC001")
        dest = self.repo.find_by_id("ACC002")

        # ACC001: 1000 - 50 + 25 = 975
        # ACC002: 500 + 50 - 25 = 525
        self.assertEqual(source.balance, Decimal("975.00"))
        self.assertEqual(dest.balance, Decimal("525.00"))

    def test_utc_timestamps(self):
        """Test that all timestamps are UTC-aware."""
        result = self.service.transfer_money("ACC001", "ACC002", Decimal("100.00"))
        self.assertTrue(result)

        # Check that account timestamps are UTC
        source = self.repo.find_by_id("ACC001")
        dest = self.repo.find_by_id("ACC002")

        self.assertEqual(source.last_updated.tzinfo, timezone.utc)
        self.assertEqual(dest.last_updated.tzinfo, timezone.utc)

    def test_transfer_recording(self):
        """Test that transfers are properly recorded for audit trail."""
        # Test with explicit transfer ID
        transfer_id = str(uuid4())
        result = self.service.transfer_money("ACC001", "ACC002", Decimal("100.00"), transfer_id)
        self.assertTrue(result)

        # Verify transfer is recorded
        self.assertTrue(self.repo.transfer_exists(transfer_id))


if __name__ == "__main__":
    # Run the test suite
    unittest.main(verbosity=2)
