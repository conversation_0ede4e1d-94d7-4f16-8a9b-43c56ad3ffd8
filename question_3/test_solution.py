"""
Test suite for the transaction processing system.

Demonstrates testing best practices including:
- Unit testing with mocks
- Integration testing
- Error scenario testing
- Async test patterns
"""

import asyncio
import pytest
from decimal import Decimal
from unittest.mock import AsyncMock, Mock

from solution import (
    PaymentRequest,
    PaymentResult,
    PaymentProcessingError,
    NotificationError,
    StripePaymentProcessor,
    EmailNotificationSender,
    TransactionService,
)


class TestPaymentRequest:
    """Test PaymentRequest validation and creation."""
    
    def test_valid_payment_request(self):
        """Test creating a valid payment request."""
        request = PaymentRequest(
            amount=Decimal("99.99"),
            card_token="tok_test_visa",
            email="<EMAIL>"
        )
        assert request.amount == Decimal("99.99")
        assert request.card_token == "tok_test_visa"
        assert request.email == "<EMAIL>"
        assert request.currency == "USD"
    
    def test_invalid_amount(self):
        """Test payment request with invalid amount."""
        with pytest.raises(ValueError, match="Payment amount must be positive"):
            PaymentRequest(
                amount=Decimal("-10.00"),
                card_token="tok_test_visa",
                email="<EMAIL>"
            )
    
    def test_invalid_email(self):
        """Test payment request with invalid email."""
        with pytest.raises(ValueError, match="Valid email address is required"):
            PaymentRequest(
                amount=Decimal("99.99"),
                card_token="tok_test_visa",
                email="invalid-email"
            )


class TestStripePaymentProcessor:
    """Test Stripe payment processor."""
    
    @pytest.mark.asyncio
    async def test_successful_payment(self):
        """Test successful payment processing."""
        processor = StripePaymentProcessor()
        request = PaymentRequest(
            amount=Decimal("99.99"),
            card_token="tok_test_visa",
            email="<EMAIL>"
        )
        
        result = await processor.charge(request)
        
        assert result.amount == Decimal("99.99")
        assert result.currency == "USD"
        assert result.status == "completed"
        assert result.transaction_id.startswith("ch_")
    
    @pytest.mark.asyncio
    async def test_payment_amount_limit_exceeded(self):
        """Test payment with amount exceeding limit."""
        processor = StripePaymentProcessor()
        request = PaymentRequest(
            amount=Decimal("15000.00"),  # Exceeds limit
            card_token="tok_test_visa",
            email="<EMAIL>"
        )
        
        with pytest.raises(PaymentProcessingError, match="Amount exceeds limit"):
            await processor.charge(request)


class TestEmailNotificationSender:
    """Test email notification sender."""
    
    @pytest.mark.asyncio
    async def test_successful_email_sending(self):
        """Test successful email notification."""
        sender = EmailNotificationSender()
        payment_result = PaymentResult.success(
            transaction_id="ch_test123",
            amount=Decimal("99.99")
        )
        
        # Should not raise any exception
        await sender.send_payment_confirmation("<EMAIL>", payment_result)
    
    @pytest.mark.asyncio
    async def test_invalid_email_failure(self):
        """Test email sending with invalid email."""
        sender = EmailNotificationSender()
        payment_result = PaymentResult.success(
            transaction_id="ch_test123",
            amount=Decimal("99.99")
        )
        
        with pytest.raises(NotificationError, match="Invalid email address"):
            await sender.send_payment_confirmation("<EMAIL>", payment_result)


class TestTransactionService:
    """Test transaction service integration."""
    
    @pytest.mark.asyncio
    async def test_successful_transaction(self):
        """Test complete successful transaction flow."""
        # Create mocks
        mock_processor = AsyncMock()
        mock_sender = AsyncMock()
        
        # Configure mock responses
        payment_result = PaymentResult.success("ch_test123", Decimal("99.99"))
        mock_processor.charge.return_value = payment_result
        
        # Create service with mocks
        service = TransactionService(mock_processor, mock_sender)
        
        # Create request
        request = PaymentRequest(
            amount=Decimal("99.99"),
            card_token="tok_test_visa",
            email="<EMAIL>"
        )
        
        # Process transaction
        result = await service.process_transaction(request)
        
        # Verify results
        assert result.transaction_id == "ch_test123"
        assert result.amount == Decimal("99.99")
        
        # Verify mocks were called correctly
        mock_processor.charge.assert_called_once_with(request)
        mock_sender.send_payment_confirmation.assert_called_once_with(
            "<EMAIL>", payment_result
        )
    
    @pytest.mark.asyncio
    async def test_payment_failure(self):
        """Test transaction with payment failure."""
        # Create mocks
        mock_processor = AsyncMock()
        mock_sender = AsyncMock()
        
        # Configure mock to raise exception
        mock_processor.charge.side_effect = PaymentProcessingError("Card declined")
        
        # Create service with mocks
        service = TransactionService(mock_processor, mock_sender)
        
        # Create request
        request = PaymentRequest(
            amount=Decimal("99.99"),
            card_token="tok_test_visa",
            email="<EMAIL>"
        )
        
        # Verify exception is raised
        with pytest.raises(PaymentProcessingError, match="Card declined"):
            await service.process_transaction(request)
        
        # Verify email was not sent
        mock_sender.send_payment_confirmation.assert_not_called()
    
    @pytest.mark.asyncio
    async def test_notification_failure_does_not_affect_payment(self):
        """Test that notification failure doesn't affect successful payment."""
        # Create mocks
        mock_processor = AsyncMock()
        mock_sender = AsyncMock()
        
        # Configure mocks
        payment_result = PaymentResult.success("ch_test123", Decimal("99.99"))
        mock_processor.charge.return_value = payment_result
        mock_sender.send_payment_confirmation.side_effect = NotificationError("Email failed")
        
        # Create service with mocks
        service = TransactionService(mock_processor, mock_sender)
        
        # Create request
        request = PaymentRequest(
            amount=Decimal("99.99"),
            card_token="tok_test_visa",
            email="<EMAIL>"
        )
        
        # Process transaction - should succeed despite notification failure
        result = await service.process_transaction(request)
        
        # Verify payment succeeded
        assert result.transaction_id == "ch_test123"
        assert result.amount == Decimal("99.99")


if __name__ == "__main__":
    # Run tests
    pytest.main([__file__, "-v"])
