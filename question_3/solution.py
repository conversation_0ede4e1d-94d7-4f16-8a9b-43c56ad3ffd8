"""
Production-ready transaction processing system with proper separation of concerns.

This module demonstrates best practices for:
- Dependency injection and inversion of control
- Abstract base classes for extensibility
- Proper error handling and logging
- Type safety with comprehensive type hints
- Configuration management
- Security considerations for payment processing
"""

import logging
import os
import time
from abc import ABC, abstractmethod
from dataclasses import dataclass
from decimal import Decimal
from typing import Optional, Protocol
from uuid import uuid4

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


@dataclass
class PaymentRequest:
    """Immutable payment request data structure."""
    amount: Decimal
    card_token: str
    email: str
    currency: str = "USD"
    
    def __post_init__(self):
        """Validate payment request data."""
        if self.amount <= 0:
            raise ValueError("Payment amount must be positive")
        if not self.card_token:
            raise ValueError("Card token is required")
        if not self.email or "@" not in self.email:
            raise ValueError("Valid email address is required")


@dataclass
class PaymentResult:
    """Payment processing result."""
    transaction_id: str
    amount: Decimal
    currency: str
    status: str
    timestamp: float
    
    @classmethod
    def success(cls, transaction_id: str, amount: Decimal, currency: str = "USD") -> "PaymentResult":
        """Create a successful payment result."""
        return cls(
            transaction_id=transaction_id,
            amount=amount,
            currency=currency,
            status="completed",
            timestamp=time.time()
        )


class PaymentProcessingError(Exception):
    """Custom exception for payment processing errors."""
    pass


class NotificationError(Exception):
    """Custom exception for notification errors."""
    pass


class PaymentProcessor(ABC):
    """Abstract base class for payment processors."""
    
    @abstractmethod
    async def charge(self, request: PaymentRequest) -> PaymentResult:
        """
        Process a payment charge.
        
        Args:
            request: Payment request containing amount, card token, etc.
            
        Returns:
            PaymentResult with transaction details
            
        Raises:
            PaymentProcessingError: If payment processing fails
        """
        pass


class NotificationSender(Protocol):
    """Protocol for notification senders."""
    
    async def send_payment_confirmation(
        self, 
        email: str, 
        payment_result: PaymentResult
    ) -> None:
        """Send payment confirmation notification."""
        pass


class StripePaymentProcessor(PaymentProcessor):
    """Stripe payment processor implementation."""
    
    def __init__(self, api_key: Optional[str] = None):
        """
        Initialize Stripe processor.
        
        Args:
            api_key: Stripe API key (defaults to environment variable)
        """
        self.api_key = api_key or os.getenv("STRIPE_API_KEY")
        if not self.api_key:
            logger.warning("No Stripe API key provided - using test mode")
    
    async def charge(self, request: PaymentRequest) -> PaymentResult:
        """
        Process payment through Stripe.
        
        Args:
            request: Payment request
            
        Returns:
            PaymentResult with Stripe transaction details
            
        Raises:
            PaymentProcessingError: If Stripe processing fails
        """
        try:
            logger.info(
                "Processing payment via Stripe: amount=%s %s", 
                request.amount, 
                request.currency
            )
            
            # Simulate Stripe API call
            # In real implementation: stripe.Charge.create(...)
            if request.amount > Decimal("10000"):
                raise PaymentProcessingError("Amount exceeds limit")
            
            # Generate realistic Stripe transaction ID
            transaction_id = f"ch_{uuid4().hex[:24]}"
            
            logger.info("Stripe payment successful: tx_id=%s", transaction_id)
            return PaymentResult.success(
                transaction_id=transaction_id,
                amount=request.amount,
                currency=request.currency
            )
            
        except Exception as e:
            logger.error("Stripe payment failed: %s", str(e))
            raise PaymentProcessingError(f"Stripe processing failed: {str(e)}") from e


class EmailNotificationSender:
    """Email notification sender implementation."""
    
    def __init__(self, smtp_config: Optional[dict] = None):
        """
        Initialize email sender.
        
        Args:
            smtp_config: SMTP configuration (defaults to environment variables)
        """
        self.smtp_config = smtp_config or {
            "host": os.getenv("SMTP_HOST", "localhost"),
            "port": int(os.getenv("SMTP_PORT", "587")),
            "username": os.getenv("SMTP_USERNAME"),
            "password": os.getenv("SMTP_PASSWORD")
        }
    
    async def send_payment_confirmation(
        self, 
        email: str, 
        payment_result: PaymentResult
    ) -> None:
        """
        Send payment confirmation email.
        
        Args:
            email: Recipient email address
            payment_result: Payment processing result
            
        Raises:
            NotificationError: If email sending fails
        """
        try:
            logger.info(
                "Sending payment confirmation to %s for transaction %s", 
                email, 
                payment_result.transaction_id
            )
            
            # Simulate email sending
            # In real implementation: use sendgrid, ses, or smtp
            message = self._format_confirmation_message(payment_result)
            
            # Simulate potential email failure
            if "invalid" in email.lower():
                raise NotificationError("Invalid email address")
            
            logger.info("Payment confirmation sent successfully to %s", email)
            
        except Exception as e:
            logger.error("Failed to send confirmation email: %s", str(e))
            raise NotificationError(f"Email sending failed: {str(e)}") from e
    
    def _format_confirmation_message(self, payment_result: PaymentResult) -> str:
        """Format payment confirmation message."""
        return f"""
        Payment Confirmation
        
        Transaction ID: {payment_result.transaction_id}
        Amount: {payment_result.amount} {payment_result.currency}
        Status: {payment_result.status}
        Date: {time.ctime(payment_result.timestamp)}
        
        Thank you for your payment!
        """


class TransactionService:
    """
    Main transaction processing service with dependency injection.
    
    This service orchestrates payment processing and notifications
    while maintaining separation of concerns.
    """
    
    def __init__(
        self, 
        payment_processor: PaymentProcessor,
        notification_sender: NotificationSender
    ):
        """
        Initialize transaction service.
        
        Args:
            payment_processor: Payment processor implementation
            notification_sender: Notification sender implementation
        """
        self._payment_processor = payment_processor
        self._notification_sender = notification_sender
        logger.info("TransactionService initialized")
    
    async def process_transaction(self, request: PaymentRequest) -> PaymentResult:
        """
        Process a complete transaction including payment and notification.
        
        Args:
            request: Payment request
            
        Returns:
            PaymentResult with transaction details
            
        Raises:
            PaymentProcessingError: If payment processing fails
            NotificationError: If notification sending fails (payment still succeeds)
        """
        logger.info(
            "Starting transaction processing: amount=%s %s, email=%s", 
            request.amount, 
            request.currency,
            request.email
        )
        
        try:
            # Process payment
            payment_result = await self._payment_processor.charge(request)
            logger.info("Payment processed successfully: %s", payment_result.transaction_id)
            
            # Send confirmation (non-blocking - payment already succeeded)
            try:
                await self._notification_sender.send_payment_confirmation(
                    request.email, 
                    payment_result
                )
            except NotificationError as e:
                # Log but don't fail the transaction
                logger.warning("Notification failed but payment succeeded: %s", str(e))
            
            logger.info("Transaction completed successfully: %s", payment_result.transaction_id)
            return payment_result
            
        except PaymentProcessingError:
            logger.error("Transaction failed during payment processing")
            raise
        except Exception as e:
            logger.error("Unexpected error during transaction processing: %s", str(e))
            raise PaymentProcessingError(f"Transaction processing failed: {str(e)}") from e


# Factory functions for dependency injection
def create_stripe_processor() -> StripePaymentProcessor:
    """Create Stripe payment processor with environment configuration."""
    return StripePaymentProcessor()


def create_email_sender() -> EmailNotificationSender:
    """Create email notification sender with environment configuration."""
    return EmailNotificationSender()


def create_transaction_service() -> TransactionService:
    """Create transaction service with default dependencies."""
    return TransactionService(
        payment_processor=create_stripe_processor(),
        notification_sender=create_email_sender()
    )


# Main application
async def main() -> None:
    """Main application entry point."""
    logger.info("Starting payment processing application")

    try:
        # Create service with dependency injection
        service = create_transaction_service()

        # Create payment request
        request = PaymentRequest(
            amount=Decimal("99.99"),
            card_token="tok_visa_4242424242424242",  # Stripe test token
            email="<EMAIL>"
        )

        # Process transaction
        result = await service.process_transaction(request)

        print("Transaction completed successfully!")
        print(f"Transaction ID: {result.transaction_id}")
        print(f"Amount: {result.amount} {result.currency}")
        print(f"Status: {result.status}")

    except PaymentProcessingError as e:
        print(f"Payment failed: {str(e)}")
        logger.error("Payment processing failed: %s", str(e))
    except Exception as e:
        print(f"Unexpected error: {str(e)}")
        logger.error("Unexpected application error: %s", str(e))


if __name__ == "__main__":
    import asyncio
    asyncio.run(main())
