### Explanation

#### 1. Upstream Services
- What: Service 1, Service 2, Service 3
- Role: Generate events that require notifications (e.g., user sign-up, payments).
- Requirement Fit: Triggers notifications for all supported channels.

#### 2. API Gateway
- Role:
  - Central entry point for all notification requests.
  - Handles routing, authentication, and load balancing.
- Requirement Fit:
  - High availability: Horizontally scalable.
  - Throttling: Works with Auth & Rate Limit to avoid abuse.

#### 3. Auth & Rate Limit Service
- Role:
  - Validates authentication tokens.
  - Applies per-user or per-service rate limits.
- Requirement Fit:
  - Throttling: Prevents spam and overload.
  - Fault tolerance: Blocks abusive requests before they hit downstream components.

#### 4. Notification Service
- Role: Orchestrates notification processing:
  - Priority & Dedup Engine: Assigns priority (critical, high, medium, low) and removes duplicate notifications.
  - Throttle / Rate-Limit: Controls system-level flow.
  - Preference Service: Enforces user notification settings.
  - Template Resolution: Fetches and personalizes message templates before enqueuing.

- Requirement Fit:
  - Priority: Ensures high-priority notifications are processed first.
  - User preferences: Respects user opt-in/out settings.
  - Spam prevention: Combines deduplication and throttling.

#### 5. Channel-Specific Queues
- What: iOS PN, Android PN, SMS, Email, and In-App queues.
- Role:
  - Persist notifications until workers deliver them.
  - Support prioritization (critical messages at the front).
- Requirement Fit:
  - Scalability: Each channel scales independently.
  - Fault tolerance: Decouples sending from processing.
  - Low latency: Priority queues deliver urgent notifications first.

#### 6. Channel-Specific Workers
- Role:
  - Pull messages from queues.
  - Deliver notifications via third-party providers (APNs, FCM, SMS, SMTP, WebSocket).
  - Retry on failures with exponential backoff.
  - Write delivery outcomes to Notification Log.
- Requirement Fit:
  - Retries: Configurable retry logic.
  - Scalability: Workers scale horizontally.
  - Fault tolerance: Isolated failures do not impact other channels.

#### 7. Third-Party Providers
- What: APNs, FCM, SMS aggregators, SMTP, WebSocket servers.
- Role: Final delivery to user devices or applications.
- Requirement Fit:
  - Multiple channels: Enables Email, SMS, Push, and In-App notifications.

#### 8. Notification Log & Status Tracker
- Role:
  - Stores full notification lifecycle (sent, delivered, failed, read).
  - Status Tracker Webhook informs upstream services about delivery outcomes.
- Requirement Fit:
  - Delivery status tracking: Provides an audit trail.
  - Reliability: Source of truth for notification delivery.

#### 9. Dead Letter Queues (DLQ)
- Role:
  - Store messages that fail after maximum retries.
  - Allow manual inspection and reprocessing.
- Requirement Fit:
  - Fault tolerance: Prevents message loss.
  - Retries: Ensures problematic notifications are not dropped silently.

